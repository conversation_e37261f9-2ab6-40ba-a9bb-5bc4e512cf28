<x-dynamic-component :component="$getEntryWrapperView()" :entry="$entry">
    @php
    $enrolledSubjects = \App\Models\SubjectEnrollment::with([
        'subject',
        'class.Schedule.room',
        'class.Faculty',
        'class.Subject',
        'class.enrollments'
    ])
        ->where('enrollment_id', $getRecord()->id)
        ->whereNull('grade')
        ->get();

    $scheduleData = [];
    $classDetails = [];
    $subjectColors = [
        'bg-blue-100 border-blue-300 text-blue-800 dark:bg-blue-900/20 dark:border-blue-700 dark:text-blue-300',
        'bg-green-100 border-green-300 text-green-800 dark:bg-green-900/20 dark:border-green-700 dark:text-green-300',
        'bg-purple-100 border-purple-300 text-purple-800 dark:bg-purple-900/20 dark:border-purple-700 dark:text-purple-300',
        'bg-orange-100 border-orange-300 text-orange-800 dark:bg-orange-900/20 dark:border-orange-700 dark:text-orange-300',
        'bg-pink-100 border-pink-300 text-pink-800 dark:bg-pink-900/20 dark:border-pink-700 dark:text-pink-300',
        'bg-indigo-100 border-indigo-300 text-indigo-800 dark:bg-indigo-900/20 dark:border-indigo-700 dark:text-indigo-300',
        'bg-teal-100 border-teal-300 text-teal-800 dark:bg-teal-900/20 dark:border-teal-700 dark:text-teal-300',
        'bg-red-100 border-red-300 text-red-800 dark:bg-red-900/20 dark:border-red-700 dark:text-red-300',
    ];

    $colorIndex = 0;

    foreach ($enrolledSubjects as $enrolledSubject) {
        if (!$enrolledSubject->subject) {
            \Log::error('Subject not found for enrolled subject:', $enrolledSubject->toArray());
            continue;
        }

        $class = $enrolledSubject->class;

        if ($class && $class->Schedule->count() > 0) {
            $subjectCode = $enrolledSubject->subject->code;
            $subjectColor = $subjectColors[$colorIndex % count($subjectColors)];
            $colorIndex++;

            // Store class details for modal
            $classDetails[$subjectCode] = [
                'class' => $class,
                'subject' => $enrolledSubject->subject,
                'color' => $subjectColor,
                'enrollment_count' => $class->enrollments->count(),
                'is_lab' => str_contains(strtolower($enrolledSubject->subject->title), 'lab') ||
                           str_contains(strtolower($enrolledSubject->subject->title), 'laboratory'),
            ];

            foreach ($class->Schedule as $schedule) {
                $weekDay = strtolower($schedule->day_of_week);
                $startTime = $schedule->formatted_start_time;
                $endTime = $schedule->formatted_end_time;
                $room = $schedule->room->name ?? 'TBA';
                $section = $class->section ?? 'N/A';

                $scheduleData[$subjectCode][$weekDay] = [
                    'time' => "$startTime - $endTime",
                    'room' => $room,
                    'section' => $section,
                    'schedule_id' => $schedule->id,
                    'class_id' => $class->id,
                    'color' => $subjectColor,
                    'is_lab' => $classDetails[$subjectCode]['is_lab'],
                ];
            }
        }
    }
    @endphp
    <!-- Enhanced Interactive Schedule Table -->
    <div class="space-y-4">
        @if(empty($scheduleData))
            <div class="text-center py-8">
                <div class="mx-auto h-12 w-12 text-gray-400">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No Schedule Available</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">This student has no class schedules assigned yet.</p>
            </div>
        @else
            <!-- Enhanced Legend with Statistics -->
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-lg p-4 mb-4 border border-gray-200 dark:border-gray-600">
                <div class="flex flex-wrap items-center justify-between gap-4">
                    <div class="flex flex-wrap gap-4">
                        <div class="flex items-center gap-2 text-sm">
                            <div class="w-4 h-4 bg-gradient-to-br from-blue-100 to-blue-200 border-2 border-blue-300 rounded-md shadow-sm"></div>
                            <span class="font-medium text-gray-700 dark:text-gray-300">Lecture Classes</span>
                        </div>
                        <div class="flex items-center gap-2 text-sm">
                            <div class="w-4 h-4 bg-gradient-to-br from-green-100 to-green-200 border-2 border-green-300 rounded-md shadow-sm"></div>
                            <span class="font-medium text-gray-700 dark:text-gray-300">Laboratory Classes</span>
                        </div>
                        <div class="flex items-center gap-2 text-sm">
                            <svg class="w-4 h-4 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.121 2.122"></path>
                            </svg>
                            <span class="text-primary-600 dark:text-primary-400 font-medium">Click slots for details</span>
                        </div>
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        <span class="font-medium">{{ count($scheduleData) }}</span> subjects enrolled
                    </div>
                </div>
            </div>

            <!-- Enhanced Responsive Schedule Table -->
            <div class="overflow-x-auto bg-white dark:bg-gray-900 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700">
                        <tr>
                            <th class="px-6 py-4 text-left text-xs font-bold text-gray-800 dark:text-gray-200 uppercase tracking-wider w-40 border-r border-gray-200 dark:border-gray-600">
                                <div class="flex items-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                    </svg>
                                    Subject
                                </div>
                            </th>
                            @foreach (['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'] as $day)
                            <th class="px-4 py-4 text-center text-xs font-bold text-gray-800 dark:text-gray-200 uppercase tracking-wider min-w-[160px] border-r border-gray-200 dark:border-gray-600 last:border-r-0">
                                <div class="flex flex-col items-center gap-1">
                                    <span>{{ $day }}</span>
                                    <div class="w-8 h-0.5 bg-primary-400 rounded-full"></div>
                                </div>
                            </th>
                            @endforeach
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach ($scheduleData as $subjectCode => $schedule)
                        <tr class="hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 dark:hover:from-gray-800 dark:hover:to-gray-700 transition-all duration-200 group">
                            <td class="px-6 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-600">
                                <div class="flex items-center space-x-3">
                                    <div class="relative">
                                        <div class="w-4 h-4 rounded-full {{ $classDetails[$subjectCode]['color'] ?? 'bg-gray-200' }} shadow-sm"></div>
                                        @if($classDetails[$subjectCode]['is_lab'] ?? false)
                                        <div class="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full border border-white dark:border-gray-900"></div>
                                        @endif
                                    </div>
                                    <div class="flex-1">
                                        <div class="text-sm font-semibold text-gray-900 dark:text-gray-100 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                                            {{ $subjectCode }}
                                        </div>
                                        @if($classDetails[$subjectCode]['is_lab'] ?? false)
                                        <div class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 mt-1">
                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                                            </svg>
                                            LAB
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            @foreach (['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'] as $day)
                            <td class="px-3 py-3 text-center border-r border-gray-200 dark:border-gray-600 last:border-r-0">
                                @if(isset($schedule[$day]))
                                    @php $slot = $schedule[$day]; @endphp
                                    <button
                                        type="button"
                                        onclick="openScheduleModal('{{ $subjectCode }}', '{{ $day }}', {{ json_encode($slot) }}, {{ json_encode($classDetails[$subjectCode] ?? []) }})"
                                        class="w-full p-3 rounded-lg border-2 transition-all duration-300 hover:shadow-lg hover:scale-105 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 group {{ $slot['color'] }} relative overflow-hidden"
                                    >
                                        <!-- Hover overlay -->
                                        <div class="absolute inset-0 bg-white/20 dark:bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>

                                        <div class="relative z-10">
                                            <!-- Room with icon -->
                                            <div class="flex items-center justify-center gap-1 text-xs font-bold mb-1">
                                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                                </svg>
                                                {{ $slot['room'] }}
                                            </div>

                                            <!-- Section -->
                                            <div class="text-xs opacity-80 font-medium mb-2">
                                                Section {{ $slot['section'] }}
                                            </div>

                                            <!-- Time with clock icon -->
                                            <div class="flex items-center justify-center gap-1 text-xs font-bold">
                                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                {{ $slot['time'] }}
                                            </div>

                                            <!-- Lab indicator -->
                                            @if($slot['is_lab'] ?? false)
                                            <div class="absolute top-1 right-1 bg-green-500 text-white text-xs px-1.5 py-0.5 rounded-full font-bold shadow-sm">
                                                LAB
                                            </div>
                                            @endif
                                        </div>
                                    </button>
                                @else
                                    <div class="w-full p-6 text-gray-300 dark:text-gray-700 text-sm flex items-center justify-center rounded-lg border-2 border-dashed border-gray-200 dark:border-gray-700">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                        </svg>
                                    </div>
                                @endif
                            </td>
                            @endforeach
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @endif
    </div>

    <!-- Schedule Detail Modal -->
    <div id="scheduleModal" class="fixed inset-0 z-50 hidden overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" onclick="closeScheduleModal()"></div>

            <!-- Modal panel -->
            <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 sm:mx-0 sm:h-10 sm:w-10">
                        <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="modal-title">
                            Class Schedule Details
                        </h3>
                        <div class="mt-4 space-y-4" id="modalContent">
                            <!-- Content will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
                <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                    @can('update', \App\Models\Classes::class)
                    <button type="button" id="editClassBtn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Class
                    </button>
                    @endcan
                    @can('view', \App\Models\Classes::class)
                    <button type="button" id="viewClassBtn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        View Class
                    </button>
                    @endcan
                    <button type="button" onclick="closeScheduleModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:w-auto sm:text-sm">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Styles for Enhanced Visual Appeal -->
    <style>
        .schedule-slot-button {
            background: linear-gradient(135deg, var(--tw-gradient-from), var(--tw-gradient-to));
            backdrop-filter: blur(10px);
        }

        .schedule-slot-button:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .schedule-table-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .dark .schedule-table-header {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        }

        .schedule-legend {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        }

        .dark .schedule-legend {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        }

        @keyframes pulse-glow {
            0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
            50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8); }
        }

        .schedule-slot-button:focus {
            animation: pulse-glow 2s infinite;
        }
    </style>

    <script>
        let currentClassId = null;

        function openScheduleModal(subjectCode, day, scheduleSlot, classDetails) {
            currentClassId = scheduleSlot.class_id;

            const modal = document.getElementById('scheduleModal');
            const modalContent = document.getElementById('modalContent');

            // Format day name
            const dayName = day.charAt(0).toUpperCase() + day.slice(1);

            // Build modal content
            modalContent.innerHTML = `
                <div class="space-y-4">
                    <!-- Subject Information -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">Subject Information</h4>
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div>
                                <span class="text-gray-500 dark:text-gray-400">Code:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">${subjectCode}</span>
                            </div>
                            <div>
                                <span class="text-gray-500 dark:text-gray-400">Type:</span>
                                <span class="font-medium ${scheduleSlot.is_lab ? 'text-green-600 dark:text-green-400' : 'text-blue-600 dark:text-blue-400'}">
                                    ${scheduleSlot.is_lab ? 'Laboratory' : 'Lecture'}
                                </span>
                            </div>
                            <div class="col-span-2">
                                <span class="text-gray-500 dark:text-gray-400">Title:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">${classDetails.subject?.title || 'N/A'}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Schedule Information -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">Schedule Details</h4>
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div>
                                <span class="text-gray-500 dark:text-gray-400">Day:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">${dayName}</span>
                            </div>
                            <div>
                                <span class="text-gray-500 dark:text-gray-400">Time:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">${scheduleSlot.time}</span>
                            </div>
                            <div>
                                <span class="text-gray-500 dark:text-gray-400">Room:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">${scheduleSlot.room}</span>
                            </div>
                            <div>
                                <span class="text-gray-500 dark:text-gray-400">Section:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">${scheduleSlot.section}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Class Information -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">Class Information</h4>
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div>
                                <span class="text-gray-500 dark:text-gray-400">Faculty:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">${classDetails.class?.faculty_full_name || 'TBA'}</span>
                            </div>
                            <div>
                                <span class="text-gray-500 dark:text-gray-400">Enrollment:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">
                                    ${classDetails.enrollment_count || 0}/${classDetails.class?.maximum_slots || 'N/A'}
                                </span>
                            </div>
                            <div>
                                <span class="text-gray-500 dark:text-gray-400">Academic Year:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">${classDetails.class?.academic_year || 'N/A'}</span>
                            </div>
                            <div>
                                <span class="text-gray-500 dark:text-gray-400">Semester:</span>
                                <span class="font-medium text-gray-900 dark:text-gray-100">${classDetails.class?.semester || 'N/A'}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Show modal
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeScheduleModal() {
            const modal = document.getElementById('scheduleModal');
            modal.classList.add('hidden');
            document.body.style.overflow = 'auto';
            currentClassId = null;
        }

        // Handle edit class button
        const editClassBtn = document.getElementById('editClassBtn');
        if (editClassBtn) {
            editClassBtn.addEventListener('click', function() {
                if (currentClassId) {
                    // Navigate to class edit page
                    window.open(`/admin/classes/${currentClassId}/edit`, '_blank');
                }
            });
        }

        // Handle view class button
        const viewClassBtn = document.getElementById('viewClassBtn');
        if (viewClassBtn) {
            viewClassBtn.addEventListener('click', function() {
                if (currentClassId) {
                    // Navigate to class view page
                    window.open(`/admin/classes/${currentClassId}`, '_blank');
                }
            });
        }

        // Close modal on escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeScheduleModal();
            }
        });
    </script>
</x-dynamic-component>
