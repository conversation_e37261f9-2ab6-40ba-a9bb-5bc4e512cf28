@php
use Illuminate\Support\Str;

// Get enrolled subjects with their class and schedule information
$enrolledSubjects = \App\Models\SubjectEnrollment::with([
    'subject',
    'class.Schedule.room',
    'class.Faculty',
    'class.Subject',
    'class.enrollments'
])
    ->where('enrollment_id', $getRecord()->id)
    ->whereNull('grade')
    ->get();

$scheduleData = [];
$classDetails = [];
$subjectColors = [];

// Generate consistent colors for subjects with dark mode support
$colorPalette = [
    'bg-blue-100 dark:bg-blue-900/30 border-blue-300 dark:border-blue-600 text-blue-800 dark:text-blue-200',
    'bg-green-100 dark:bg-green-900/30 border-green-300 dark:border-green-600 text-green-800 dark:text-green-200',
    'bg-purple-100 dark:bg-purple-900/30 border-purple-300 dark:border-purple-600 text-purple-800 dark:text-purple-200',
    'bg-yellow-100 dark:bg-yellow-900/30 border-yellow-300 dark:border-yellow-600 text-yellow-800 dark:text-yellow-200',
    'bg-pink-100 dark:bg-pink-900/30 border-pink-300 dark:border-pink-600 text-pink-800 dark:text-pink-200',
    'bg-indigo-100 dark:bg-indigo-900/30 border-indigo-300 dark:border-indigo-600 text-indigo-800 dark:text-indigo-200',
    'bg-red-100 dark:bg-red-900/30 border-red-300 dark:border-red-600 text-red-800 dark:text-red-200',
    'bg-orange-100 dark:bg-orange-900/30 border-orange-300 dark:border-orange-600 text-orange-800 dark:text-orange-200',
    'bg-teal-100 dark:bg-teal-900/30 border-teal-300 dark:border-teal-600 text-teal-800 dark:text-teal-200',
    'bg-cyan-100 dark:bg-cyan-900/30 border-cyan-300 dark:border-cyan-600 text-cyan-800 dark:text-cyan-200',
];

$colorIndex = 0;

foreach ($enrolledSubjects as $enrollment) {
    if (!$enrollment->subject || !$enrollment->class) {
        continue;
    }

    $subjectCode = $enrollment->subject->code;
    $class = $enrollment->class;
    
    // Assign color to subject if not already assigned
    if (!isset($subjectColors[$subjectCode])) {
        $subjectColors[$subjectCode] = $colorPalette[$colorIndex % count($colorPalette)];
        $colorIndex++;
    }

    // Store class details for the modal
    $classDetails[$class->id] = [
        'id' => $class->id,
        'subject' => $enrollment->subject,
        'class' => $class,
        'faculty' => $class->Faculty,
        'enrollment_count' => $class->enrollments->count(),
        'color' => $subjectColors[$subjectCode]
    ];

    // Process schedules
    if ($class->Schedule) {
        foreach ($class->Schedule as $schedule) {
            $weekDay = strtolower($schedule->day_of_week);
            $startTime = $schedule->start_time->format('g:i A');
            $endTime = $schedule->end_time->format('g:i A');
            $room = $schedule->room->name ?? 'TBA';
            
            // Determine if this is a lab or lecture based on room name or classification
            $isLab = str_contains(strtolower($room), 'lab') ||
                     str_contains(strtolower($class->classification ?? ''), 'lab') ||
                     str_contains(strtolower($enrollment->subject->title), 'laboratory');

            $scheduleData[$subjectCode][$weekDay] = [
                'time' => "$startTime - $endTime",
                'room' => $room,
                'section' => $class->section ?? 'N/A',
                'class_id' => $class->id,
                'schedule_id' => $schedule->id,
                'color' => $subjectColors[$subjectCode],
                'subject_title' => $enrollment->subject->title,
                'classification' => $class->classification ?? 'Regular',
                'is_lab' => $isLab,
                'duration' => $schedule->start_time->diffInMinutes($schedule->end_time) . ' min',
                'subject_units' => $enrollment->subject->units ?? 0
            ];
        }
    }
}

$days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
$dayLabels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
@endphp

<x-dynamic-component :component="$getEntryWrapperView()" :entry="$entry">
    <div class="enhanced-schedule-container">
        @if(empty($scheduleData))
            <div class="text-center py-8">
                <div class="mx-auto h-12 w-12 text-gray-400">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No Schedule Available</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">No subjects enrolled or schedules assigned yet.</p>
            </div>
        @else
            <div class="space-y-4">
            <!-- Schedule Summary -->
            @php
                $totalSubjects = count($scheduleData);
                $totalScheduleSlots = 0;
                $labCount = 0;
                $lectureCount = 0;

                foreach($scheduleData as $schedule) {
                    foreach($schedule as $slot) {
                        $totalScheduleSlots++;
                        if($slot['is_lab']) {
                            $labCount++;
                        } else {
                            $lectureCount++;
                        }
                    }
                }
            @endphp

            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $totalSubjects }}</div>
                    <div class="text-sm text-blue-600 dark:text-blue-400">Subjects</div>
                </div>
                <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $totalScheduleSlots }}</div>
                    <div class="text-sm text-green-600 dark:text-green-400">Schedule Slots</div>
                </div>
                <div class="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ $lectureCount }}</div>
                    <div class="text-sm text-purple-600 dark:text-purple-400">Lectures</div>
                </div>
                <div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">{{ $labCount }}</div>
                    <div class="text-sm text-orange-600 dark:text-orange-400">Laboratories</div>
                </div>
            </div>

            <!-- Legend -->
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                        </svg>
                        Subject Legend
                    </h4>
                    <span class="text-xs text-gray-500 dark:text-gray-400">{{ count($subjectColors) }} subjects</span>
                </div>
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                    @foreach($subjectColors as $subjectCode => $color)
                        <div class="flex items-center space-x-2 bg-white dark:bg-gray-800 rounded-md p-2 border border-gray-200 dark:border-gray-600">
                            <div class="w-3 h-3 rounded-full border-2 {{ $color }} flex-shrink-0"></div>
                            <span class="text-xs font-medium text-gray-700 dark:text-gray-300 truncate">{{ $subjectCode }}</span>
                        </div>
                    @endforeach
                </div>
            </div>

            <!-- Schedule Table -->
            <div class="overflow-x-auto bg-white dark:bg-gray-900 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
                <div class="min-w-full">
                    <div class="bg-gray-50 dark:bg-gray-800 px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z"></path>
                            </svg>
                            Weekly Schedule
                            <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">(Click any slot for details)</span>
                        </h3>
                    </div>
                </div>
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Subject
                            </th>
                            @foreach($dayLabels as $dayLabel)
                                <th class="px-4 py-4 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    {{ $dayLabel }}
                                </th>
                            @endforeach
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach($scheduleData as $subjectCode => $schedule)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-3 h-3 rounded border-2 {{ $subjectColors[$subjectCode] }}"></div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                {{ $subjectCode }}
                                            </div>
                                            @if(isset($schedule[array_key_first($schedule)]['subject_title']))
                                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                                    {{ Str::limit($schedule[array_key_first($schedule)]['subject_title'], 30) }}
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                @foreach($days as $day)
                                    <td class="px-2 py-2 text-center">
                                        @if(isset($schedule[$day]))
                                            @php $slot = $schedule[$day]; @endphp
                                            <button
                                                type="button"
                                                onclick="openScheduleModal({{ json_encode($slot) }}, {{ json_encode($classDetails[$slot['class_id']] ?? []) }})"
                                                class="group w-full p-3 rounded-lg border-2 transition-all duration-200 hover:shadow-lg hover:scale-105 cursor-pointer {{ $slot['color'] }} relative overflow-hidden"
                                            >
                                                <!-- Lab/Lecture indicator -->
                                                @if($slot['is_lab'])
                                                    <div class="absolute top-1 right-1 w-2 h-2 bg-orange-500 rounded-full" title="Laboratory"></div>
                                                @else
                                                    <div class="absolute top-1 right-1 w-2 h-2 bg-blue-500 rounded-full" title="Lecture"></div>
                                                @endif

                                                <div class="text-xs font-medium flex items-center justify-between">
                                                    <span>{{ $slot['room'] }}</span>
                                                    @if($slot['is_lab'])
                                                        <span class="text-xs bg-orange-200 dark:bg-orange-800 px-1 rounded">LAB</span>
                                                    @endif
                                                </div>
                                                <div class="text-xs font-medium">{{ $slot['section'] }}</div>
                                                <div class="text-xs mt-1 opacity-75">{{ $slot['time'] }}</div>
                                                <div class="text-xs mt-1 opacity-60">{{ $slot['duration'] }}</div>

                                                @if($slot['classification'] !== 'Regular')
                                                    <div class="text-xs mt-1 font-semibold bg-white/20 dark:bg-black/20 px-1 rounded">
                                                        {{ $slot['classification'] }}
                                                    </div>
                                                @endif

                                                <!-- Hover effect -->
                                                <div class="absolute inset-0 bg-white/10 dark:bg-black/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg"></div>
                                            </button>
                                        @else
                                            <div class="w-full p-3 text-gray-300 dark:text-gray-600 border-2 border-dashed border-gray-200 dark:border-gray-700 rounded-lg">
                                                <div class="text-xs text-center">—</div>
                                            </div>
                                        @endif
                                    </td>
                                @endforeach
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            </div>
        @endif

        <!-- Modal for Schedule Details -->
    <div id="scheduleModal" class="fixed inset-0 z-50 hidden overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" onclick="closeScheduleModal()"></div>
            
            <!-- Modal panel -->
            <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="modal-title">
                                Class Schedule Details
                            </h3>
                            <div class="mt-4 space-y-4" id="modalContent">
                                <!-- Content will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse space-y-2 sm:space-y-0 sm:space-x-3 sm:space-x-reverse">
                    <button type="button" id="editClassBtn" class="w-full inline-flex items-center justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:w-auto sm:text-sm transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Class
                    </button>
                    <button type="button" id="viewClassBtn" class="w-full inline-flex items-center justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:w-auto sm:text-sm transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        View Class
                    </button>
                    <button type="button" onclick="closeScheduleModal()" class="w-full inline-flex items-center justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:w-auto sm:text-sm transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openScheduleModal(slot, classDetails) {
            const modal = document.getElementById('scheduleModal');
            const content = document.getElementById('modalContent');
            const editBtn = document.getElementById('editClassBtn');
            
            // Build modal content
            let html = `
                <div class="grid grid-cols-1 gap-4">
                    <!-- Schedule Information -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border-l-4 ${slot.is_lab ? 'border-orange-500' : 'border-blue-500'}">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-medium text-gray-900 dark:text-gray-100">Schedule Information</h4>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${slot.is_lab ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'}">
                                ${slot.is_lab ? '🧪 Laboratory' : '📚 Lecture'}
                            </span>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Time:</span>
                                <span class="font-medium">${slot.time}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Duration:</span>
                                <span class="font-medium">${slot.duration}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Room:</span>
                                <span class="font-medium">${slot.room}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Section:</span>
                                <span class="font-medium">${slot.section}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Classification:</span>
                                <span class="font-medium">${slot.classification}</span>
                            </div>
                        </div>
                    </div>
            `;

            if (classDetails && classDetails.subject) {
                html += `
                    <!-- Subject Information -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">📖 Subject Information</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Code:</span>
                                <span class="font-medium font-mono bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded">${classDetails.subject.code}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Title:</span>
                                <span class="font-medium">${classDetails.subject.title}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Units:</span>
                                <span class="font-medium">${classDetails.subject.units || 'N/A'}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Lecture Hours:</span>
                                <span class="font-medium">${classDetails.subject.lecture || 0}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Laboratory Hours:</span>
                                <span class="font-medium">${classDetails.subject.laboratory || 0}</span>
                            </div>
                        </div>
                    </div>
                `;
            }

            if (classDetails && classDetails.faculty) {
                html += `
                    <!-- Faculty Information -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">👨‍🏫 Faculty Information</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Instructor:</span>
                                <span class="font-medium">${classDetails.faculty.full_name || 'TBA'}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Department:</span>
                                <span class="font-medium">${classDetails.faculty.department || 'N/A'}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Email:</span>
                                <span class="font-medium">${classDetails.faculty.email || 'N/A'}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Office Hours:</span>
                                <span class="font-medium">${classDetails.faculty.office_hours || 'N/A'}</span>
                            </div>
                        </div>
                    </div>
                `;
            }

            if (classDetails && classDetails.class) {
                const maxSlots = classDetails.class.maximum_slots || 0;
                const currentEnrollment = classDetails.enrollment_count || 0;
                const enrollmentPercentage = maxSlots > 0 ? Math.round((currentEnrollment / maxSlots) * 100) : 0;
                const isNearFull = enrollmentPercentage >= 80;
                const isFull = enrollmentPercentage >= 100;

                html += `
                    <!-- Class Information -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">🏫 Class Information</h4>
                        <div class="space-y-3 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Class ID:</span>
                                <span class="font-medium font-mono bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded">#${classDetails.class.id}</span>
                            </div>

                            <!-- Enrollment Progress -->
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Enrollment:</span>
                                    <span class="font-medium">${currentEnrollment}${maxSlots > 0 ? ` / ${maxSlots}` : ''}</span>
                                </div>
                                ${maxSlots > 0 ? `
                                    <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                                        <div class="h-2 rounded-full transition-all duration-300 ${isFull ? 'bg-red-500' : isNearFull ? 'bg-yellow-500' : 'bg-green-500'}" style="width: ${Math.min(enrollmentPercentage, 100)}%"></div>
                                    </div>
                                    <div class="text-xs text-center ${isFull ? 'text-red-600 dark:text-red-400' : isNearFull ? 'text-yellow-600 dark:text-yellow-400' : 'text-green-600 dark:text-green-400'}">
                                        ${enrollmentPercentage}% Full ${isFull ? '(Class Full)' : isNearFull ? '(Nearly Full)' : ''}
                                    </div>
                                ` : ''}
                            </div>

                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Academic Year:</span>
                                <span class="font-medium">${classDetails.class.academic_year || 'N/A'}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">School Year:</span>
                                <span class="font-medium">${classDetails.class.school_year || 'N/A'}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Semester:</span>
                                <span class="font-medium">${classDetails.class.semester || 'N/A'}</span>
                            </div>
                        </div>
                    </div>
                `;
            }

            html += '</div>';
            content.innerHTML = html;
            
            // Set up buttons
            const viewBtn = document.getElementById('viewClassBtn');

            if (classDetails && classDetails.class) {
                editBtn.onclick = function() {
                    window.open(`/admin/classes/${classDetails.class.id}/edit`, '_blank');
                };
                viewBtn.onclick = function() {
                    window.open(`/admin/classes/${classDetails.class.id}`, '_blank');
                };
                editBtn.style.display = 'inline-flex';
                viewBtn.style.display = 'inline-flex';
            } else {
                editBtn.style.display = 'none';
                viewBtn.style.display = 'none';
            }
            
            modal.classList.remove('hidden');
        }

        function closeScheduleModal() {
            document.getElementById('scheduleModal').classList.add('hidden');
        }

        // Close modal on Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeScheduleModal();
            }
        });
        </script>
    </div>
</x-dynamic-component>
